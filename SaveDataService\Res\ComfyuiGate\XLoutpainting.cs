﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService;
using SaveDataService.Manage;
using SaveDataService.Server.Websocket;

namespace ComfyuiGate
{
    /// <summary>
    /// XLoutpainting - ComfyUI工作流调用类
    /// 基于文件: XLoutpainting.json
    /// 自动生成时间: 2025-06-05 17:32:58
    /// 继承RESTfulAPIBase，自动提供RESTful API功能
    /// </summary>
    public class XLoutpainting : RESTfulAPIBase
    {
        /// <summary>
        /// 工作流JSON定义
        /// </summary>
        private const string WORKFLOW_JSON = @"{""id"":""79350e4e-20b9-49e9-b85a-eaa28263d98a"",""revision"":0,""last_node_id"":215,""last_link_id"":864,""nodes"":[{""id"":39,""type"":""LoadImage"",""pos"":[821.8904418945312,1243.2294921875],""size"":[315,314.00006103515625],""flags"":{},""order"":0,""mode"":0,""inputs"":[{""localized_name"":""图像"",""name"":""image"",""type"":""COMBO"",""widget"":{""name"":""image""},""link"":null},{""localized_name"":""选择文件上传"",""name"":""upload"",""type"":""IMAGEUPLOAD"",""widget"":{""name"":""upload""},""link"":null}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[]},{""localized_name"":""遮罩"",""name"":""MASK"",""type"":""MASK"",""links"":null}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""LoadImage"",""widget_ue_connectable"":{""image"":true,""upload"":true}},""widgets_values"":[""pexels-jtucker-954254 (1).jpg"",""image""],""color"":""#223"",""bgcolor"":""#335""},{""id"":77,""type"":""PrepImageForClipVision"",""pos"":[813.8904418945312,1081.2294921875],""size"":[315,106],""flags"":{},""order"":16,""mode"":0,""inputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":728},{""localized_name"":""interpolation"",""name"":""interpolation"",""type"":""COMBO"",""widget"":{""name"":""interpolation""},""link"":null},{""localized_name"":""crop_position"",""name"":""crop_position"",""type"":""COMBO"",""widget"":{""name"":""crop_position""},""link"":null},{""localized_name"":""sharpening"",""name"":""sharpening"",""type"":""FLOAT"",""widget"":{""name"":""sharpening""},""link"":null}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[543]}],""properties"":{""cnr_id"":""comfyui_ipadapter_plus"",""ver"":""2.0.0"",""Node name for S&R"":""PrepImageForClipVision"",""widget_ue_connectable"":{""interpolation"":true,""crop_position"":true,""sharpening"":true}},""widgets_values"":[""LANCZOS"",""left"",0],""color"":""#223"",""bgcolor"":""#335""},{""id"":19,""type"":""CLIPTextEncode"",""pos"":[871.560546875,734.4476318359375],""size"":[397.1569519042969,124.60340881347656],""flags"":{},""order"":9,""mode"":0,""inputs"":[{""localized_name"":""clip"",""name"":""clip"",""type"":""CLIP"",""link"":29},{""localized_name"":""文本"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":null}],""outputs"":[{""localized_name"":""条件"",""name"":""CONDITIONING"",""type"":""CONDITIONING"",""slot_index"":0,""links"":[648]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""CLIPTextEncode"",""widget_ue_connectable"":{""text"":true}},""widgets_values"":["""",[false,true]],""color"":""#322"",""bgcolor"":""#533""},{""id"":175,""type"":""INPAINT_VAEEncodeInpaintConditioning"",""pos"":[1622,580],""size"":[292.20001220703125,106],""flags"":{},""order"":21,""mode"":0,""inputs"":[{""localized_name"":""positive"",""name"":""positive"",""type"":""CONDITIONING"",""link"":647},{""localized_name"":""negative"",""name"":""negative"",""type"":""CONDITIONING"",""link"":648},{""localized_name"":""vae"",""name"":""vae"",""type"":""VAE"",""link"":649},{""localized_name"":""pixels"",""name"":""pixels"",""type"":""IMAGE"",""link"":839},{""localized_name"":""mask"",""name"":""mask"",""type"":""MASK"",""link"":752}],""outputs"":[{""localized_name"":""positive"",""name"":""positive"",""type"":""CONDITIONING"",""slot_index"":0,""links"":[672]},{""localized_name"":""negative"",""name"":""negative"",""type"":""CONDITIONING"",""slot_index"":1,""links"":[673]},{""localized_name"":""latent_inpaint"",""name"":""latent_inpaint"",""type"":""LATENT"",""slot_index"":2,""links"":[816]},{""localized_name"":""latent_samples"",""name"":""latent_samples"",""type"":""LATENT"",""slot_index"":3,""links"":[817]}],""properties"":{""cnr_id"":""comfyui-inpaint-nodes"",""ver"":""1.0.4"",""Node name for S&R"":""INPAINT_VAEEncodeInpaintConditioning"",""widget_ue_connectable"":{}},""widgets_values"":[]},{""id"":173,""type"":""INPAINT_MaskedFill"",""pos"":[-95.75188446044922,386.6933288574219],""size"":[315,102],""flags"":{},""order"":17,""mode"":0,""inputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":637},{""localized_name"":""mask"",""name"":""mask"",""type"":""MASK"",""link"":770},{""localized_name"":""fill"",""name"":""fill"",""type"":""COMBO"",""widget"":{""name"":""fill""},""link"":null},{""localized_name"":""falloff"",""name"":""falloff"",""type"":""INT"",""widget"":{""name"":""falloff""},""link"":null}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[809]}],""properties"":{""cnr_id"":""comfyui-inpaint-nodes"",""ver"":""1.0.4"",""Node name for S&R"":""INPAINT_MaskedFill"",""widget_ue_connectable"":{""fill"":true,""falloff"":true}},""widgets_values"":[""telea"",0]},{""id"":15,""type"":""CheckpointLoaderSimple"",""pos"":[767,303],""size"":[426.86322021484375,98],""flags"":{},""order"":1,""mode"":0,""inputs"":[{""localized_name"":""Checkpoint名称"",""name"":""ckpt_name"",""type"":""COMBO"",""widget"":{""name"":""ckpt_name""},""link"":null}],""outputs"":[{""localized_name"":""模型"",""name"":""MODEL"",""type"":""MODEL"",""slot_index"":0,""links"":[841]},{""localized_name"":""CLIP"",""name"":""CLIP"",""type"":""CLIP"",""slot_index"":1,""links"":[27,29]},{""localized_name"":""VAE"",""name"":""VAE"",""type"":""VAE"",""slot_index"":2,""links"":[25,649]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""CheckpointLoaderSimple"",""widget_ue_connectable"":{""ckpt_name"":true}},""widgets_values"":[""juggernautXL_ragnarokBy.safetensors""]},{""id"":205,""type"":""GetImageSize+"",""pos"":[-935.8598022460938,740.3443603515625],""size"":[159.50155639648438,66],""flags"":{},""order"":10,""mode"":0,""inputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":844}],""outputs"":[{""localized_name"":""width"",""name"":""width"",""type"":""INT"",""links"":[]},{""localized_name"":""height"",""name"":""height"",""type"":""INT"",""links"":[]},{""localized_name"":""count"",""name"":""count"",""type"":""INT"",""links"":null}],""properties"":{""cnr_id"":""comfyui_essentials"",""ver"":""1.1.0"",""Node name for S&R"":""GetImageSize+"",""widget_ue_connectable"":{}},""widgets_values"":[]},{""id"":209,""type"":""LayerUtility: ImageScaleByAspectRatio"",""pos"":[-985.1697998046875,373.5687561035156],""size"":[342.6714782714844,306],""flags"":{},""order"":11,""mode"":0,""inputs"":[{""localized_name"":""图像"",""name"":""image"",""shape"":7,""type"":""IMAGE"",""link"":849},{""localized_name"":""遮罩"",""name"":""mask"",""shape"":7,""type"":""MASK"",""link"":null},{""localized_name"":""宽高比"",""name"":""aspect_ratio"",""type"":""COMBO"",""widget"":{""name"":""aspect_ratio""},""link"":null},{""localized_name"":""比例宽度"",""name"":""proportional_width"",""type"":""INT"",""widget"":{""name"":""proportional_width""},""link"":null},{""localized_name"":""比例高度"",""name"":""proportional_height"",""type"":""INT"",""widget"":{""name"":""proportional_height""},""link"":null},{""localized_name"":""适应"",""name"":""fit"",""type"":""COMBO"",""widget"":{""name"":""fit""},""link"":null},{""localized_name"":""方法"",""name"":""method"",""type"":""COMBO"",""widget"":{""name"":""method""},""link"":null},{""localized_name"":""四舍五入到倍数"",""name"":""round_to_multiple"",""type"":""COMBO"",""widget"":{""name"":""round_to_multiple""},""link"":null},{""localized_name"":""缩放至最长边"",""name"":""scale_to_longest_side"",""type"":""BOOLEAN"",""widget"":{""name"":""scale_to_longest_side""},""link"":null},{""localized_name"":""最长边"",""name"":""longest_side"",""type"":""INT"",""widget"":{""name"":""longest_side""},""link"":null}],""outputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""links"":[850,851]},{""localized_name"":""mask"",""name"":""mask"",""type"":""MASK"",""links"":null},{""localized_name"":""original_size"",""name"":""original_size"",""type"":""BOX"",""links"":null},{""localized_name"":""width"",""name"":""width"",""type"":""INT"",""links"":null},{""localized_name"":""height"",""name"":""height"",""type"":""INT"",""links"":null}],""properties"":{""cnr_id"":""comfyui_layerstyle"",""ver"":""1.0.90"",""Node name for S&R"":""LayerUtility: ImageScaleByAspectRatio"",""widget_ue_connectable"":{""aspect_ratio"":true,""proportional_width"":true,""proportional_height"":true,""fit"":true,""method"":true,""round_to_multiple"":true,""scale_to_longest_side"":true,""longest_side"":true}},""widgets_values"":[""original"",2,1,""letterbox"",""lanczos"",""8"",true,1024],""color"":""rgba(38, 73, 116, 0.7)""},{""id"":17,""type"":""VAEDecode"",""pos"":[1679.5543212890625,1319.0804443359375],""size"":[210,46],""flags"":{},""order"":25,""mode"":0,""inputs"":[{""localized_name"":""Latent"",""name"":""samples"",""type"":""LATENT"",""link"":749},{""localized_name"":""vae"",""name"":""vae"",""type"":""VAE"",""link"":25}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[251]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""VAEDecode"",""widget_ue_connectable"":{}},""widgets_values"":[],""color"":""#323"",""bgcolor"":""#535""},{""id"":66,""type"":""PreviewBridge"",""pos"":[2088.599365234375,254.97386169433594],""size"":[727.852294921875,603.7252197265625],""flags"":{},""order"":27,""mode"":0,""inputs"":[{""localized_name"":""images"",""name"":""images"",""type"":""IMAGE"",""link"":251},{""localized_name"":""image"",""name"":""image"",""type"":""STRING"",""widget"":{""name"":""image""},""link"":null},{""localized_name"":""block"",""name"":""block"",""shape"":7,""type"":""BOOLEAN"",""widget"":{""name"":""block""},""link"":null},{""localized_name"":""restore_mask"",""name"":""restore_mask"",""shape"":7,""type"":""COMBO"",""widget"":{""name"":""restore_mask""},""link"":null}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[794]},{""localized_name"":""遮罩"",""name"":""MASK"",""type"":""MASK"",""slot_index"":1,""links"":[]}],""properties"":{""cnr_id"":""comfyui-impact-pack"",""ver"":""8.14.2"",""Node name for S&R"":""PreviewBridge"",""widget_ue_connectable"":{""image"":true,""block"":true,""restore_mask"":true}},""widgets_values"":[""$66-0"",false,""never""],""color"":""#233"",""bgcolor"":""#355""},{""id"":207,""type"":""GetImageSizeAndCount"",""pos"":[-751.7984619140625,731.3580932617188],""size"":[190.86483764648438,86],""flags"":{},""order"":13,""mode"":0,""inputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":850}],""outputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""links"":null},{""label"":""688 width"",""localized_name"":""width"",""name"":""width"",""type"":""INT"",""links"":[]},{""label"":""1024 height"",""localized_name"":""height"",""name"":""height"",""type"":""INT"",""links"":[]},{""label"":""1 count"",""localized_name"":""count"",""name"":""count"",""type"":""INT"",""links"":null}],""properties"":{""cnr_id"":""comfyui-kjnodes"",""ver"":""1.1.0"",""Node name for S&R"":""GetImageSizeAndCount"",""widget_ue_connectable"":{}},""widgets_values"":[]},{""id"":41,""type"":""CLIPVisionLoader"",""pos"":[1205.8902587890625,1159.2294921875],""size"":[315,58],""flags"":{},""order"":2,""mode"":0,""inputs"":[{""localized_name"":""clip名称"",""name"":""clip_name"",""type"":""COMBO"",""widget"":{""name"":""clip_name""},""link"":null}],""outputs"":[{""localized_name"":""CLIP视觉"",""name"":""CLIP_VISION"",""type"":""CLIP_VISION"",""links"":[94]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""CLIPVisionLoader"",""widget_ue_connectable"":{""clip_name"":true}},""widgets_values"":[""clip_vision_h.safetensors""],""color"":""#223"",""bgcolor"":""#335""},{""id"":186,""type"":""INPAINT_MaskedBlur"",""pos"":[318.0837097167969,368.993408203125],""size"":[315,102],""flags"":{},""order"":20,""mode"":0,""inputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":809},{""localized_name"":""mask"",""name"":""mask"",""type"":""MASK"",""link"":771},{""localized_name"":""blur"",""name"":""blur"",""type"":""INT"",""widget"":{""name"":""blur""},""link"":null},{""localized_name"":""falloff"",""name"":""falloff"",""type"":""INT"",""widget"":{""name"":""falloff""},""link"":null}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[839]}],""properties"":{""cnr_id"":""comfyui-inpaint-nodes"",""ver"":""1.0.4"",""Node name for S&R"":""INPAINT_MaskedBlur"",""widget_ue_connectable"":{""blur"":true,""falloff"":true}},""widgets_values"":[40,0]},{""id"":139,""type"":""ImagePadForOutpaintMasked"",""pos"":[-542.459228515625,346.4622497558594],""size"":[315,174],""flags"":{},""order"":14,""mode"":0,""inputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":851},{""localized_name"":""mask"",""name"":""mask"",""shape"":7,""type"":""MASK"",""link"":null},{""localized_name"":""left"",""name"":""left"",""type"":""INT"",""widget"":{""name"":""left""},""link"":null},{""localized_name"":""top"",""name"":""top"",""type"":""INT"",""widget"":{""name"":""top""},""link"":null},{""localized_name"":""right"",""name"":""right"",""type"":""INT"",""widget"":{""name"":""right""},""link"":null},{""localized_name"":""bottom"",""name"":""bottom"",""type"":""INT"",""widget"":{""name"":""bottom""},""link"":null},{""localized_name"":""feathering"",""name"":""feathering"",""type"":""INT"",""widget"":{""name"":""feathering""},""link"":null}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[637,728,810]},{""localized_name"":""遮罩"",""name"":""MASK"",""type"":""MASK"",""slot_index"":1,""links"":[752,770,771,795,838]}],""properties"":{""cnr_id"":""comfyui-kjnodes"",""ver"":""1.1.0"",""Node name for S&R"":""ImagePadForOutpaintMasked"",""widget_ue_connectable"":{""left"":true,""top"":true,""right"":true,""bottom"":true,""feathering"":true}},""widgets_values"":[0,0,256,0,60]},{""id"":210,""type"":""Florence2Run"",""pos"":[66.96595764160156,1062.020751953125],""size"":[400,364],""flags"":{},""order"":12,""mode"":0,""inputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":860},{""localized_name"":""florence2_model"",""name"":""florence2_model"",""type"":""FL2MODEL"",""link"":856},{""localized_name"":""text_input"",""name"":""text_input"",""type"":""STRING"",""widget"":{""name"":""text_input""},""link"":null},{""localized_name"":""task"",""name"":""task"",""type"":""COMBO"",""widget"":{""name"":""task""},""link"":null},{""localized_name"":""fill_mask"",""name"":""fill_mask"",""type"":""BOOLEAN"",""widget"":{""name"":""fill_mask""},""link"":null},{""localized_name"":""keep_model_loaded"",""name"":""keep_model_loaded"",""shape"":7,""type"":""BOOLEAN"",""widget"":{""name"":""keep_model_loaded""},""link"":null},{""localized_name"":""max_new_tokens"",""name"":""max_new_tokens"",""shape"":7,""type"":""INT"",""widget"":{""name"":""max_new_tokens""},""link"":null},{""localized_name"":""num_beams"",""name"":""num_beams"",""shape"":7,""type"":""INT"",""widget"":{""name"":""num_beams""},""link"":null},{""localized_name"":""do_sample"",""name"":""do_sample"",""shape"":7,""type"":""BOOLEAN"",""widget"":{""name"":""do_sample""},""link"":null},{""localized_name"":""output_mask_select"",""name"":""output_mask_select"",""shape"":7,""type"":""STRING"",""widget"":{""name"":""output_mask_select""},""link"":null},{""localized_name"":""seed"",""name"":""seed"",""shape"":7,""type"":""INT"",""widget"":{""name"":""seed""},""link"":null}],""outputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""links"":null},{""localized_name"":""mask"",""name"":""mask"",""type"":""MASK"",""links"":null},{""localized_name"":""caption"",""name"":""caption"",""type"":""STRING"",""links"":[857]},{""localized_name"":""data"",""name"":""data"",""type"":""JSON"",""links"":null}],""properties"":{""cnr_id"":""comfyui-florence2"",""ver"":""1.0.3"",""Node name for S&R"":""Florence2Run"",""widget_ue_connectable"":{""text_input"":true,""task"":true,""fill_mask"":true,""keep_model_loaded"":true,""max_new_tokens"":true,""num_beams"":true,""do_sample"":true,""output_mask_select"":true,""seed"":true}},""widgets_values"":["""",""detailed_caption"",true,false,1024,3,true,"""",592518792303588,""randomize"",[false,true]]},{""id"":169,""type"":""INPAINT_ApplyFooocusInpaint"",""pos"":[1697.4671630859375,749.6885375976562],""size"":[210,66],""flags"":{},""order"":22,""mode"":0,""inputs"":[{""localized_name"":""model"",""name"":""model"",""type"":""MODEL"",""link"":743},{""localized_name"":""patch"",""name"":""patch"",""type"":""INPAINT_PATCH"",""link"":590},{""localized_name"":""latent"",""name"":""latent"",""type"":""LATENT"",""link"":816}],""outputs"":[{""localized_name"":""模型"",""name"":""MODEL"",""type"":""MODEL"",""slot_index"":0,""links"":[741]}],""properties"":{""cnr_id"":""comfyui-inpaint-nodes"",""ver"":""1.0.4"",""Node name for S&R"":""INPAINT_ApplyFooocusInpaint"",""widget_ue_connectable"":{}},""widgets_values"":[]},{""id"":61,""type"":""DifferentialDiffusion"",""pos"":[1687.20068359375,884.22119140625],""size"":[216.77987670898438,26],""flags"":{},""order"":23,""mode"":0,""inputs"":[{""localized_name"":""模型"",""name"":""model"",""type"":""MODEL"",""link"":741}],""outputs"":[{""localized_name"":""模型"",""name"":""MODEL"",""type"":""MODEL"",""slot_index"":0,""links"":[761]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""DifferentialDiffusion"",""widget_ue_connectable"":{}},""widgets_values"":[],""color"":""#323"",""bgcolor"":""#535""},{""id"":170,""type"":""INPAINT_LoadFooocusInpaint"",""pos"":[1614,413],""size"":[315,82],""flags"":{},""order"":3,""mode"":0,""inputs"":[{""localized_name"":""head"",""name"":""head"",""type"":""COMBO"",""widget"":{""name"":""head""},""link"":null},{""localized_name"":""patch"",""name"":""patch"",""type"":""COMBO"",""widget"":{""name"":""patch""},""link"":null}],""outputs"":[{""localized_name"":""INPAINT_PATCH"",""name"":""INPAINT_PATCH"",""type"":""INPAINT_PATCH"",""slot_index"":0,""links"":[590]}],""properties"":{""cnr_id"":""comfyui-inpaint-nodes"",""ver"":""1.0.4"",""Node name for S&R"":""INPAINT_LoadFooocusInpaint"",""widget_ue_connectable"":{""head"":true,""patch"":true}},""widgets_values"":[""fooocus_inpaint_head.pth"",""inpaint_v26.fooocus.patch""]},{""id"":193,""type"":""RemoveNoiseMask"",""pos"":[1677.6026611328125,1444.244140625],""size"":[210,26],""flags"":{},""order"":26,""mode"":0,""inputs"":[{""localized_name"":""samples"",""name"":""samples"",""type"":""LATENT"",""link"":779}],""outputs"":[{""localized_name"":""Latent"",""name"":""LATENT"",""type"":""LATENT"",""slot_index"":0,""links"":[]}],""properties"":{""cnr_id"":""comfyui-impact-pack"",""ver"":""8.14.2"",""Node name for S&R"":""RemoveNoiseMask"",""widget_ue_connectable"":{}},""widgets_values"":[],""color"":""#323"",""bgcolor"":""#535""},{""id"":16,""type"":""KSampler"",""pos"":[1618.629150390625,1002.4096069335938],""size"":[315,262],""flags"":{},""order"":24,""mode"":0,""inputs"":[{""localized_name"":""模型"",""name"":""model"",""type"":""MODEL"",""link"":761},{""localized_name"":""正面条件"",""name"":""positive"",""type"":""CONDITIONING"",""link"":672},{""localized_name"":""负面条件"",""name"":""negative"",""type"":""CONDITIONING"",""link"":673},{""localized_name"":""Latent图像"",""name"":""latent_image"",""type"":""LATENT"",""link"":817},{""localized_name"":""种子"",""name"":""seed"",""type"":""INT"",""widget"":{""name"":""seed""},""link"":null},{""localized_name"":""步数"",""name"":""steps"",""type"":""INT"",""widget"":{""name"":""steps""},""link"":null},{""localized_name"":""cfg"",""name"":""cfg"",""type"":""FLOAT"",""widget"":{""name"":""cfg""},""link"":null},{""localized_name"":""采样器名称"",""name"":""sampler_name"",""type"":""COMBO"",""widget"":{""name"":""sampler_name""},""link"":null},{""localized_name"":""调度器"",""name"":""scheduler"",""type"":""COMBO"",""widget"":{""name"":""scheduler""},""link"":null},{""localized_name"":""降噪"",""name"":""denoise"",""type"":""FLOAT"",""widget"":{""name"":""denoise""},""link"":null}],""outputs"":[{""localized_name"":""Latent"",""name"":""LATENT"",""type"":""LATENT"",""slot_index"":0,""links"":[749,779]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""KSampler"",""widget_ue_connectable"":{""seed"":true,""steps"":true,""cfg"":true,""sampler_name"":true,""scheduler"":true,""denoise"":true}},""widgets_values"":[321016536324652,""randomize"",20,7,""dpmpp_2m_sde_gpu"",""karras"",1],""color"":""#323"",""bgcolor"":""#535""},{""id"":18,""type"":""CLIPTextEncode"",""pos"":[868.7049560546875,489.849609375],""size"":[384.80865478515625,138.21595764160156],""flags"":{},""order"":18,""mode"":0,""inputs"":[{""localized_name"":""clip"",""name"":""clip"",""type"":""CLIP"",""link"":27},{""localized_name"":""文本"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":859}],""outputs"":[{""localized_name"":""条件"",""name"":""CONDITIONING"",""type"":""CONDITIONING"",""slot_index"":0,""links"":[647]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""CLIPTextEncode"",""widget_ue_connectable"":{""text"":true}},""widgets_values"":["""",[false,true]],""color"":""#232"",""bgcolor"":""#353""},{""id"":212,""type"":""ShowText|pysssss"",""pos"":[480.34033203125,1061.130859375],""size"":[210,369.4089660644531],""flags"":{},""order"":15,""mode"":0,""inputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""STRING"",""link"":857}],""outputs"":[{""localized_name"":""字符串"",""name"":""STRING"",""shape"":6,""type"":""STRING"",""links"":[859]}],""properties"":{""cnr_id"":""comfyui-custom-scripts"",""ver"":""1.2.5"",""Node name for S&R"":""ShowText|pysssss"",""widget_ue_connectable"":{}},""widgets_values"":[""The image shows a magical anime-style illustration of a woman wearing a purple and black dress and a pointed hat, riding on the back of a white unicorn. The woman has long white hair and a mysterious expression on her face, and the unicorn has a white mane and tail. The background is filled with a magical aura, giving the image a magical and whimsical feel.""]},{""id"":40,""type"":""IPAdapterModelLoader"",""pos"":[1176.4072265625,1037.8026123046875],""size"":[315,58],""flags"":{},""order"":4,""mode"":0,""inputs"":[{""localized_name"":""ipadapter_file"",""name"":""ipadapter_file"",""type"":""COMBO"",""widget"":{""name"":""ipadapter_file""},""link"":null}],""outputs"":[{""localized_name"":""IPADAPTER"",""name"":""IPADAPTER"",""type"":""IPADAPTER"",""links"":[93]}],""properties"":{""cnr_id"":""comfyui_ipadapter_plus"",""ver"":""2.0.0"",""Node name for S&R"":""IPAdapterModelLoader"",""widget_ue_connectable"":{""ipadapter_file"":true}},""widgets_values"":[""ip-adapter-plus_sdxl_vit-h.safetensors""],""color"":""#223"",""bgcolor"":""#335""},{""id"":37,""type"":""IPAdapterAdvanced"",""pos"":[1192.1485595703125,1258.1728515625],""size"":[315,278],""flags"":{},""order"":19,""mode"":0,""inputs"":[{""localized_name"":""model"",""name"":""model"",""type"":""MODEL"",""link"":841},{""localized_name"":""ipadapter"",""name"":""ipadapter"",""type"":""IPADAPTER"",""link"":93},{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":543},{""localized_name"":""image_negative"",""name"":""image_negative"",""shape"":7,""type"":""IMAGE"",""link"":null},{""localized_name"":""attn_mask"",""name"":""attn_mask"",""shape"":7,""type"":""MASK"",""link"":838},{""localized_name"":""clip_vision"",""name"":""clip_vision"",""shape"":7,""type"":""CLIP_VISION"",""link"":94},{""localized_name"":""weight"",""name"":""weight"",""type"":""FLOAT"",""widget"":{""name"":""weight""},""link"":null},{""localized_name"":""weight_type"",""name"":""weight_type"",""type"":""COMBO"",""widget"":{""name"":""weight_type""},""link"":null},{""localized_name"":""combine_embeds"",""name"":""combine_embeds"",""type"":""COMBO"",""widget"":{""name"":""combine_embeds""},""link"":null},{""localized_name"":""start_at"",""name"":""start_at"",""type"":""FLOAT"",""widget"":{""name"":""start_at""},""link"":null},{""localized_name"":""end_at"",""name"":""end_at"",""type"":""FLOAT"",""widget"":{""name"":""end_at""},""link"":null},{""localized_name"":""embeds_scaling"",""name"":""embeds_scaling"",""type"":""COMBO"",""widget"":{""name"":""embeds_scaling""},""link"":null}],""outputs"":[{""localized_name"":""模型"",""name"":""MODEL"",""type"":""MODEL"",""slot_index"":0,""links"":[743]}],""properties"":{""cnr_id"":""comfyui_ipadapter_plus"",""ver"":""2.0.0"",""Node name for S&R"":""IPAdapterAdvanced"",""widget_ue_connectable"":{""weight"":true,""weight_type"":true,""combine_embeds"":true,""start_at"":true,""end_at"":true,""embeds_scaling"":true}},""widgets_values"":[0.30000000000000004,""style transfer"",""concat"",0,1,""V only""],""color"":""#223"",""bgcolor"":""#335""},{""id"":213,""type"":""LoadImage"",""pos"":[-822.4500732421875,1108.767578125],""size"":[270,314],""flags"":{},""order"":5,""mode"":0,""inputs"":[{""localized_name"":""图像"",""name"":""image"",""type"":""COMBO"",""widget"":{""name"":""image""},""link"":null},{""localized_name"":""选择文件上传"",""name"":""upload"",""type"":""IMAGEUPLOAD"",""widget"":{""name"":""upload""},""link"":null}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""links"":[860]},{""localized_name"":""遮罩"",""name"":""MASK"",""type"":""MASK"",""links"":null}],""title"":""input-image-提示图片"",""properties"":{""widget_ue_connectable"":{},""cnr_id"":""comfy-core"",""ver"":""0.3.39"",""Node name for S&R"":""LoadImage""},""widgets_values"":[""00000-360291223.png"",""image""]},{""id"":4,""type"":""LoadImage"",""pos"":[-1559.135009765625,279.64447021484375],""size"":[483.65692138671875,514.4353637695312],""flags"":{},""order"":6,""mode"":0,""inputs"":[{""localized_name"":""图像"",""name"":""image"",""type"":""COMBO"",""widget"":{""name"":""image""},""link"":null},{""localized_name"":""选择文件上传"",""name"":""upload"",""type"":""IMAGEUPLOAD"",""widget"":{""name"":""upload""},""link"":null}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[844,849]},{""localized_name"":""遮罩"",""name"":""MASK"",""type"":""MASK"",""slot_index"":1,""links"":[]}],""title"":""input-image-操作的图片"",""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""LoadImage"",""widget_ue_connectable"":{""image"":true,""upload"":true}},""widgets_values"":[""ComfyUI_temp_gfpbc_00003_.png"",""image""]},{""id"":211,""type"":""Florence2ModelLoader"",""pos"":[-330.9937438964844,1087.9639892578125],""size"":[270,106],""flags"":{},""order"":7,""mode"":0,""inputs"":[{""localized_name"":""lora"",""name"":""lora"",""shape"":7,""type"":""PEFTLORA"",""link"":null},{""localized_name"":""model"",""name"":""model"",""type"":""COMBO"",""widget"":{""name"":""model""},""link"":null},{""localized_name"":""precision"",""name"":""precision"",""type"":""COMBO"",""widget"":{""name"":""precision""},""link"":null},{""localized_name"":""attention"",""name"":""attention"",""type"":""COMBO"",""widget"":{""name"":""attention""},""link"":null}],""outputs"":[{""localized_name"":""florence2_model"",""name"":""florence2_model"",""type"":""FL2MODEL"",""links"":[856]}],""properties"":{""cnr_id"":""comfyui-florence2"",""ver"":""1.0.3"",""Node name for S&R"":""Florence2ModelLoader"",""widget_ue_connectable"":{""model"":true,""precision"":true,""attention"":true}},""widgets_values"":[""Florence-2-Flux-Large"",""fp16"",""sdpa""]},{""id"":188,""type"":""ImageCompositeMasked"",""pos"":[2831.857421875,318.0821838378906],""size"":[315,146],""flags"":{},""order"":28,""mode"":0,""inputs"":[{""localized_name"":""目标图像"",""name"":""destination"",""type"":""IMAGE"",""link"":810},{""localized_name"":""来源图像"",""name"":""source"",""type"":""IMAGE"",""link"":794},{""localized_name"":""遮罩"",""name"":""mask"",""shape"":7,""type"":""MASK"",""link"":795},{""localized_name"":""x"",""name"":""x"",""type"":""INT"",""widget"":{""name"":""x""},""link"":null},{""localized_name"":""y"",""name"":""y"",""type"":""INT"",""widget"":{""name"":""y""},""link"":null},{""localized_name"":""缩放来源图像"",""name"":""resize_source"",""type"":""BOOLEAN"",""widget"":{""name"":""resize_source""},""link"":null}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[863]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""ImageCompositeMasked"",""widget_ue_connectable"":{""x"":true,""y"":true,""resize_source"":true}},""widgets_values"":[0,0,false]},{""id"":215,""type"":""CR Text"",""pos"":[2782.38916015625,17.154565811157227],""size"":[400,200],""flags"":{},""order"":8,""mode"":0,""inputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":null}],""outputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""*"",""links"":[864]},{""localized_name"":""show_help"",""name"":""show_help"",""type"":""STRING"",""links"":null}],""title"":""input-text-文件保存前缀"",""properties"":{""widget_ue_connectable"":{},""cnr_id"":""ComfyUI_Comfyroll_CustomNodes"",""ver"":""d78b780ae43fcf8c6b7c6505e6ffb4584281ceca"",""Node name for S&R"":""CR Text""},""widgets_values"":["""",[false,true]]},{""id"":214,""type"":""SaveImage"",""pos"":[3229.6015625,325.1398620605469],""size"":[270,58],""flags"":{},""order"":29,""mode"":0,""inputs"":[{""localized_name"":""图片"",""name"":""images"",""type"":""IMAGE"",""link"":863},{""localized_name"":""文件名前缀"",""name"":""filename_prefix"",""type"":""STRING"",""widget"":{""name"":""filename_prefix""},""link"":864}],""outputs"":[],""title"":""output-image-阔图保存的图片"",""properties"":{""widget_ue_connectable"":{},""cnr_id"":""comfy-core"",""ver"":""0.3.39"",""Node name for S&R"":""SaveImage""},""widgets_values"":[""ComfyUI""]}],""links"":[[25,15,2,17,1,""VAE""],[27,15,1,18,0,""CLIP""],[29,15,1,19,0,""CLIP""],[93,40,0,37,1,""IPADAPTER""],[94,41,0,37,5,""CLIP_VISION""],[251,17,0,66,0,""IMAGE""],[543,77,0,37,2,""IMAGE""],[590,170,0,169,1,""INPAINT_PATCH""],[637,139,0,173,0,""IMAGE""],[647,18,0,175,0,""CONDITIONING""],[648,19,0,175,1,""CONDITIONING""],[649,15,2,175,2,""VAE""],[672,175,0,16,1,""CONDITIONING""],[673,175,1,16,2,""CONDITIONING""],[728,139,0,77,0,""IMAGE""],[741,169,0,61,0,""MODEL""],[743,37,0,169,0,""MODEL""],[749,16,0,17,0,""LATENT""],[752,139,1,175,4,""MASK""],[761,61,0,16,0,""MODEL""],[770,139,1,173,1,""MASK""],[771,139,1,186,1,""MASK""],[779,16,0,193,0,""LATENT""],[794,66,0,188,1,""IMAGE""],[795,139,1,188,2,""MASK""],[809,173,0,186,0,""IMAGE""],[810,139,0,188,0,""IMAGE""],[816,175,2,169,2,""LATENT""],[817,175,3,16,3,""LATENT""],[838,139,1,37,4,""MASK""],[839,186,0,175,3,""IMAGE""],[841,15,0,37,0,""MODEL""],[844,4,0,205,0,""IMAGE""],[849,4,0,209,0,""IMAGE""],[850,209,0,207,0,""IMAGE""],[851,209,0,139,0,""IMAGE""],[856,211,0,210,1,""FL2MODEL""],[857,210,2,212,0,""STRING""],[859,212,0,18,1,""STRING""],[860,213,0,210,0,""IMAGE""],[863,188,0,214,0,""IMAGE""],[864,215,0,214,1,""STRING""]],""groups"":[{""id"":1,""title"":""IPAdapter"",""bounding"":[776,940,768,618],""color"":""#3f789e"",""font_size"":24,""flags"":{}},{""id"":2,""title"":""Latent Space"",""bounding"":[1577,263,437,1296],""color"":""#3f789e"",""font_size"":24,""flags"":{}},{""id"":3,""title"":""Group"",""bounding"":[767,431,546,476],""color"":""#3f789e"",""font_size"":24,""flags"":{}},{""id"":4,""title"":""Group"",""bounding"":[-1675,241,1061,710],""color"":""#3f789e"",""font_size"":24,""flags"":{}},{""id"":5,""title"":""Group"",""bounding"":[-549,240,1189,714],""color"":""#3f789e"",""font_size"":24,""flags"":{}}],""config"":{},""extra"":{""frontendVersion"":""1.18.9"",""VHS_latentpreview"":false,""VHS_latentpreviewrate"":0,""VHS_MetadataImage"":true,""VHS_KeepIntermediate"":true,""ue_links"":[],""ds"":{""scale"":0.9744913929161583,""offset"":[-1752.7925535104137,72.8068750960137]}},""version"":0.4}";

        /// <summary>
        /// 运行工作流
        /// </summary>
        /// <param name="image_image">input-image-提示图片 - image</param>
        /// <param name="image_upload">input-image-提示图片 - upload</param>
        /// <param name="image_image1">input-image-操作的图片 - image</param>
        /// <param name="image_upload1">input-image-操作的图片 - upload</param>
        /// <param name="text_text">input-text-文件保存前缀 - text</param>
        /// <returns>任务ID</returns>
        public static string runWorkflow(string image_image = "00000-360291223.png", string image_upload = "image", string image_image1 = "ComfyUI_temp_gfpbc_00003_.png", string image_upload1 = "image", string text_text = "")
        {
            try
            {
                // 解析工作流JSON
                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);
                if (workflow == null)
                {
                    throw new Exception("无法解析工作流JSON");
                }

                // 更新输入参数
                // 检查JSON格式并相应更新参数
                if (workflow["nodes"] != null)
                {
                    // 新格式：包含nodes数组
                    var nodesArray = workflow["nodes"] as JArray;
                    if (nodesArray != null)
                    {
                        // 更新节点 213 的参数
                        var node213 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "213") as JObject;
                        if (node213 != null)
                        {
                            var inputs213 = node213["inputs"] as JArray;
                            var widgetValues213 = node213["widgets_values"] as JArray;
                            if (inputs213 != null && widgetValues213 != null)
                            {
                                // 更新 image 参数
                                var inputIndeximage = inputs213.ToList().FindIndex(i => i["name"]?.ToString() == "image");
                                if (inputIndeximage >= 0 && inputIndeximage < widgetValues213.Count)
                                {
                                    widgetValues213[inputIndeximage] = JToken.FromObject(image_image);
                                }

                                // 更新 upload 参数
                                var inputIndexupload = inputs213.ToList().FindIndex(i => i["name"]?.ToString() == "upload");
                                if (inputIndexupload >= 0 && inputIndexupload < widgetValues213.Count)
                                {
                                    widgetValues213[inputIndexupload] = JToken.FromObject(image_upload);
                                }

                            }
                        }

                        // 更新节点 4 的参数
                        var node4 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "4") as JObject;
                        if (node4 != null)
                        {
                            var inputs4 = node4["inputs"] as JArray;
                            var widgetValues4 = node4["widgets_values"] as JArray;
                            if (inputs4 != null && widgetValues4 != null)
                            {
                                // 更新 image 参数
                                var inputIndeximage = inputs4.ToList().FindIndex(i => i["name"]?.ToString() == "image");
                                if (inputIndeximage >= 0 && inputIndeximage < widgetValues4.Count)
                                {
                                    widgetValues4[inputIndeximage] = JToken.FromObject(image_image1);
                                }

                                // 更新 upload 参数
                                var inputIndexupload = inputs4.ToList().FindIndex(i => i["name"]?.ToString() == "upload");
                                if (inputIndexupload >= 0 && inputIndexupload < widgetValues4.Count)
                                {
                                    widgetValues4[inputIndexupload] = JToken.FromObject(image_upload1);
                                }

                            }
                        }

                        // 更新节点 215 的参数
                        var node215 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "215") as JObject;
                        if (node215 != null)
                        {
                            var inputs215 = node215["inputs"] as JArray;
                            var widgetValues215 = node215["widgets_values"] as JArray;
                            if (inputs215 != null && widgetValues215 != null)
                            {
                                // 更新 text 参数
                                var inputIndextext = inputs215.ToList().FindIndex(i => i["name"]?.ToString() == "text");
                                if (inputIndextext >= 0 && inputIndextext < widgetValues215.Count)
                                {
                                    widgetValues215[inputIndextext] = JToken.FromObject(text_text);
                                }

                            }
                        }

                    }
                }
                else
                {
                    // 旧格式：直接以节点ID为键
                    // 更新节点 213 的 image 参数
                    if (workflow["213"]?["inputs"]?["image"] != null)
                    {
                        workflow["213"]["inputs"]["image"] = JToken.FromObject(image_image);
                    }

                    // 更新节点 213 的 upload 参数
                    if (workflow["213"]?["inputs"]?["upload"] != null)
                    {
                        workflow["213"]["inputs"]["upload"] = JToken.FromObject(image_upload);
                    }

                    // 更新节点 4 的 image 参数
                    if (workflow["4"]?["inputs"]?["image"] != null)
                    {
                        workflow["4"]["inputs"]["image"] = JToken.FromObject(image_image1);
                    }

                    // 更新节点 4 的 upload 参数
                    if (workflow["4"]?["inputs"]?["upload"] != null)
                    {
                        workflow["4"]["inputs"]["upload"] = JToken.FromObject(image_upload1);
                    }

                    // 更新节点 215 的 text 参数
                    if (workflow["215"]?["inputs"]?["text"] != null)
                    {
                        workflow["215"]["inputs"]["text"] = JToken.FromObject(text_text);
                    }

                }
                // 提交工作流到ComfyUI
                string updatedJson = JsonConvert.SerializeObject(workflow);
                var comfyUIManage = ComfyUIManage.Instance;

                // 首先将工作流保存到数据库（如果不存在）
                string workflowId = EnsureWorkflowExists(updatedJson, "XLoutpainting");
                if (string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine("无法保存工作流到数据库");
                    return "";
                }

                // 创建任务
                string taskId = comfyUIManage.CreateTask(workflowId, "XLoutpainting_Task", "system");
                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("无法创建任务");
                    return "";
                }

                Console.WriteLine($"工作流已提交，任务ID: {taskId}");
                return taskId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行工作流失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态信息</returns>
        public static string GetTaskStatus(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    return JsonConvert.SerializeObject(task, Formatting.Indented);
                }
                return "任务不存在";
            }
            catch (Exception ex)
            {
                return $"获取任务状态失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 确保工作流存在于数据库中
        /// </summary>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流ID</returns>
        private static string EnsureWorkflowExists(string workflowJson, string workflowName)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 计算工作流的哈希值作为唯一标识
                string workflowHash = System.Security.Cryptography.SHA1.Create()
                    .ComputeHash(System.Text.Encoding.UTF8.GetBytes(workflowJson))
                    .Aggregate("", (s, b) => s + b.ToString("x2"));

                // 检查是否已存在
                var existingWorkflow = comfyUIManage.GetWorkflowById(workflowHash);
                if (existingWorkflow != null)
                {
                    Console.WriteLine($"工作流已存在: {workflowHash}");
                    return workflowHash;
                }

                // 创建新的工作流
                string workflowId = comfyUIManage.AddWorkflow(
                    workflowName,
                    workflowJson,
                    "generated",
                    $"自动生成的工作流: {workflowName}",
                    "system"
                );

                Console.WriteLine($"创建新工作流: {workflowId}");
                return workflowId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"确保工作流存在失败: {ex.Message}");
                return "";
            }
        }

    }
}
