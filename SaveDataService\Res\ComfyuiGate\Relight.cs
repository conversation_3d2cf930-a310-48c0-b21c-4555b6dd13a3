﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService;
using SaveDataService.Manage;
using SaveDataService.Server.Websocket;

namespace ComfyuiGate
{
    /// <summary>
    /// Relight - ComfyUI工作流调用类
    /// 基于文件: relight.json
    /// 自动生成时间: 2025-06-05 17:32:58
    /// 继承RESTfulAPIBase，自动提供RESTful API功能
    /// </summary>
    public class Relight : RESTfulAPIBase
    {
        /// <summary>
        /// 工作流JSON定义
        /// </summary>
        private const string WORKFLOW_JSON = @"{""id"":""12ff3de8-88ff-4eb5-8e12-c14f41200123"",""revision"":0,""last_node_id"":76,""last_link_id"":149,""nodes"":[{""id"":56,""type"":""VAEEncode"",""pos"":[3029.023681640625,1045.09130859375],""size"":[198.2010040283203,46],""flags"":{},""order"":11,""mode"":0,""inputs"":[{""label"":""图像"",""localized_name"":""像素"",""name"":""pixels"",""type"":""IMAGE"",""link"":113},{""label"":""VAE"",""localized_name"":""vae"",""name"":""vae"",""type"":""VAE"",""link"":115}],""outputs"":[{""label"":""Latent"",""localized_name"":""Latent"",""name"":""LATENT"",""type"":""LATENT"",""slot_index"":0,""links"":[114]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.30"",""Node name for S&R"":""VAEEncode"",""widget_ue_connectable"":{}},""widgets_values"":[]},{""id"":51,""type"":""CLIPTextEncode"",""pos"":[3019.023681640625,1155.09130859375],""size"":[211.6783905029297,100.8108139038086],""flags"":{},""order"":3,""mode"":0,""inputs"":[{""label"":""CLIP"",""localized_name"":""clip"",""name"":""clip"",""type"":""CLIP"",""link"":104},{""localized_name"":""文本"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":null}],""outputs"":[{""label"":""条件"",""localized_name"":""条件"",""name"":""CONDITIONING"",""type"":""CONDITIONING"",""slot_index"":0,""links"":[106]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.30"",""Node name for S&R"":""CLIPTextEncode"",""widget_ue_connectable"":{""text"":true}},""widgets_values"":[""bright light"",[false,true]]},{""id"":52,""type"":""CLIPTextEncode"",""pos"":[3017.712646484375,1301.9813232421875],""size"":[218.74574279785156,88],""flags"":{},""order"":4,""mode"":0,""inputs"":[{""label"":""CLIP"",""localized_name"":""clip"",""name"":""clip"",""type"":""CLIP"",""link"":105},{""localized_name"":""文本"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":null}],""outputs"":[{""label"":""条件"",""localized_name"":""条件"",""name"":""CONDITIONING"",""type"":""CONDITIONING"",""slot_index"":0,""links"":[107]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.30"",""Node name for S&R"":""CLIPTextEncode"",""widget_ue_connectable"":{""text"":true}},""widgets_values"":["""",[false,true]]},{""id"":64,""type"":""ImpactMinMax"",""pos"":[2889.298828125,621.9183959960938],""size"":[210,78],""flags"":{},""order"":7,""mode"":0,""inputs"":[{""label"":""A"",""localized_name"":""a"",""name"":""a"",""type"":""*"",""link"":136},{""label"":""B"",""localized_name"":""b"",""name"":""b"",""type"":""*"",""link"":137},{""localized_name"":""mode"",""name"":""mode"",""type"":""BOOLEAN"",""widget"":{""name"":""mode""},""link"":null}],""outputs"":[{""label"":""整数"",""localized_name"":""整数"",""name"":""INT"",""type"":""INT"",""slot_index"":0,""links"":[138]}],""properties"":{""cnr_id"":""comfyui-impact-pack"",""ver"":""38bb9ffdf66386dc3a9d764db83f7460f458bffa"",""Node name for S&R"":""ImpactMinMax"",""widget_ue_connectable"":{""mode"":true}},""widgets_values"":[false]},{""id"":14,""type"":""DSINE-NormalMapPreprocessor"",""pos"":[2878.845458984375,423.90020751953125],""size"":[210,106],""flags"":{},""order"":8,""mode"":0,""inputs"":[{""label"":""图像"",""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":89},{""localized_name"":""fov"",""name"":""fov"",""shape"":7,""type"":""FLOAT"",""widget"":{""name"":""fov""},""link"":null},{""localized_name"":""iterations"",""name"":""iterations"",""shape"":7,""type"":""INT"",""widget"":{""name"":""iterations""},""link"":null},{""localized_name"":""resolution"",""name"":""resolution"",""shape"":7,""type"":""INT"",""widget"":{""name"":""resolution""},""link"":138}],""outputs"":[{""label"":""图像"",""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[93]}],""properties"":{""cnr_id"":""comfyui_controlnet_aux"",""ver"":""83463c2e4b04e729268e57f638b4212e0da4badc"",""Node name for S&R"":""DSINE-NormalMapPreprocessor"",""widget_ue_connectable"":{""fov"":true,""iterations"":true,""resolution"":true}},""widgets_values"":[0.02,1,512]},{""id"":61,""type"":""LoraLoaderModelOnly"",""pos"":[2374.82177734375,968.2453002929688],""size"":[319.0426025390625,109.00098419189453],""flags"":{},""order"":2,""mode"":4,""inputs"":[{""label"":""模型"",""localized_name"":""模型"",""name"":""model"",""type"":""MODEL"",""link"":127},{""localized_name"":""LoRA名称"",""name"":""lora_name"",""type"":""COMBO"",""widget"":{""name"":""lora_name""},""link"":null},{""localized_name"":""模型强度"",""name"":""strength_model"",""type"":""FLOAT"",""widget"":{""name"":""strength_model""},""link"":null}],""outputs"":[{""label"":""模型"",""localized_name"":""模型"",""name"":""MODEL"",""type"":""MODEL"",""slot_index"":0,""links"":[128]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.30"",""Node name for S&R"":""LoraLoaderModelOnly"",""widget_ue_connectable"":{""lora_name"":true,""strength_model"":true}},""widgets_values"":[""LCM_LoRA_Weights_SD15.safetensors"",0.4]},{""id"":48,""type"":""CheckpointLoaderSimple"",""pos"":[2374.6015625,806.2640380859375],""size"":[315,98],""flags"":{},""order"":0,""mode"":0,""inputs"":[{""localized_name"":""Checkpoint名称"",""name"":""ckpt_name"",""type"":""COMBO"",""widget"":{""name"":""ckpt_name""},""link"":null}],""outputs"":[{""label"":""模型"",""localized_name"":""模型"",""name"":""MODEL"",""type"":""MODEL"",""slot_index"":0,""links"":[127]},{""label"":""CLIP"",""localized_name"":""CLIP"",""name"":""CLIP"",""type"":""CLIP"",""slot_index"":1,""links"":[104,105]},{""label"":""VAE"",""localized_name"":""VAE"",""name"":""VAE"",""type"":""VAE"",""slot_index"":2,""links"":[102,115,116]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.30"",""Node name for S&R"":""CheckpointLoaderSimple"",""widget_ue_connectable"":{""ckpt_name"":true}},""widgets_values"":[""dreamshaper_8.safetensors""]},{""id"":49,""type"":""KSamplerAdvanced"",""pos"":[3314.09765625,749.2909545898438],""size"":[315,546],""flags"":{},""order"":13,""mode"":0,""inputs"":[{""label"":""模型"",""localized_name"":""模型"",""name"":""model"",""type"":""MODEL"",""link"":100},{""label"":""正面条件"",""localized_name"":""正面条件"",""name"":""positive"",""type"":""CONDITIONING"",""link"":106},{""label"":""负面条件"",""localized_name"":""负面条件"",""name"":""negative"",""type"":""CONDITIONING"",""link"":107},{""label"":""Latent"",""localized_name"":""Latent图像"",""name"":""latent_image"",""type"":""LATENT"",""link"":114},{""localized_name"":""添加噪波"",""name"":""add_noise"",""type"":""COMBO"",""widget"":{""name"":""add_noise""},""link"":null},{""localized_name"":""随机种"",""name"":""noise_seed"",""type"":""INT"",""widget"":{""name"":""noise_seed""},""link"":null},{""localized_name"":""步数"",""name"":""steps"",""type"":""INT"",""widget"":{""name"":""steps""},""link"":null},{""localized_name"":""cfg"",""name"":""cfg"",""type"":""FLOAT"",""widget"":{""name"":""cfg""},""link"":null},{""localized_name"":""采样器名称"",""name"":""sampler_name"",""type"":""COMBO"",""widget"":{""name"":""sampler_name""},""link"":null},{""localized_name"":""调度器"",""name"":""scheduler"",""type"":""COMBO"",""widget"":{""name"":""scheduler""},""link"":null},{""localized_name"":""开始步数"",""name"":""start_at_step"",""type"":""INT"",""widget"":{""name"":""start_at_step""},""link"":null},{""localized_name"":""结束步数"",""name"":""end_at_step"",""type"":""INT"",""widget"":{""name"":""end_at_step""},""link"":null},{""localized_name"":""返回剩余噪波"",""name"":""return_with_leftover_noise"",""type"":""COMBO"",""widget"":{""name"":""return_with_leftover_noise""},""link"":null}],""outputs"":[{""label"":""Latent"",""localized_name"":""Latent"",""name"":""LATENT"",""type"":""LATENT"",""slot_index"":0,""links"":[101]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.30"",""Node name for S&R"":""KSamplerAdvanced"",""widget_ue_connectable"":{""add_noise"":true,""noise_seed"":true,""steps"":true,""cfg"":true,""sampler_name"":true,""scheduler"":true,""start_at_step"":true,""end_at_step"":true,""return_with_leftover_noise"":true}},""widgets_values"":[""enable"",884078753467353,""fixed"",8,1.5,""lcm"",""exponential"",4,10000,""disable""]},{""id"":42,""type"":""LayerUtility: ImageScaleByAspectRatio V2"",""pos"":[2480,370],""size"":[336,330],""flags"":{},""order"":5,""mode"":0,""inputs"":[{""label"":""图像"",""localized_name"":""图像"",""name"":""image"",""shape"":7,""type"":""IMAGE"",""link"":141},{""label"":""遮罩"",""localized_name"":""遮罩"",""name"":""mask"",""shape"":7,""type"":""MASK"",""link"":null},{""localized_name"":""宽高比"",""name"":""aspect_ratio"",""type"":""COMBO"",""widget"":{""name"":""aspect_ratio""},""link"":null},{""localized_name"":""比例宽度"",""name"":""proportional_width"",""type"":""INT"",""widget"":{""name"":""proportional_width""},""link"":null},{""localized_name"":""比例高度"",""name"":""proportional_height"",""type"":""INT"",""widget"":{""name"":""proportional_height""},""link"":null},{""localized_name"":""适应"",""name"":""fit"",""type"":""COMBO"",""widget"":{""name"":""fit""},""link"":null},{""localized_name"":""方法"",""name"":""method"",""type"":""COMBO"",""widget"":{""name"":""method""},""link"":null},{""localized_name"":""四舍五入到倍数"",""name"":""round_to_multiple"",""type"":""COMBO"",""widget"":{""name"":""round_to_multiple""},""link"":null},{""localized_name"":""缩放至边"",""name"":""scale_to_side"",""type"":""COMBO"",""widget"":{""name"":""scale_to_side""},""link"":null},{""localized_name"":""缩放至长度"",""name"":""scale_to_length"",""type"":""INT"",""widget"":{""name"":""scale_to_length""},""link"":null},{""localized_name"":""背景颜色"",""name"":""background_color"",""type"":""STRING"",""widget"":{""name"":""background_color""},""link"":null}],""outputs"":[{""label"":""图像"",""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""slot_index"":0,""links"":[89,118,132,143]},{""label"":""遮罩"",""localized_name"":""mask"",""name"":""mask"",""type"":""MASK"",""slot_index"":1,""links"":null},{""label"":""原始大小"",""localized_name"":""original_size"",""name"":""original_size"",""type"":""BOX"",""links"":null},{""localized_name"":""width"",""name"":""width"",""type"":""INT"",""slot_index"":3,""links"":[136]},{""localized_name"":""height"",""name"":""height"",""type"":""INT"",""slot_index"":4,""links"":[137]}],""properties"":{""cnr_id"":""comfyui_layerstyle"",""ver"":""a085d8ca3cf3e3e8624ac0bb37a474d9d329e60e"",""Node name for S&R"":""LayerUtility: ImageScaleByAspectRatio V2"",""widget_ue_connectable"":{""aspect_ratio"":true,""proportional_width"":true,""proportional_height"":true,""fit"":true,""method"":true,""round_to_multiple"":true,""scale_to_side"":true,""scale_to_length"":true,""background_color"":true}},""widgets_values"":[""original"",1,1,""letterbox"",""lanczos"",""64"",""longest"",1280,""#000000""],""color"":""rgba(38, 73, 116, 0.7)""},{""id"":67,""type"":""LoadImage"",""pos"":[2119.52587890625,317.944091796875],""size"":[315,314],""flags"":{},""order"":1,""mode"":0,""inputs"":[{""localized_name"":""图像"",""name"":""image"",""type"":""COMBO"",""widget"":{""name"":""image""},""link"":null},{""localized_name"":""选择文件上传"",""name"":""upload"",""type"":""IMAGEUPLOAD"",""widget"":{""name"":""upload""},""link"":null}],""outputs"":[{""label"":""图像"",""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""links"":[141]},{""label"":""遮罩"",""localized_name"":""遮罩"",""name"":""MASK"",""type"":""MASK"",""links"":null}],""title"":""input-image-打光原图"",""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.30"",""Node name for S&R"":""LoadImage"",""widget_ue_connectable"":{""image"":true,""upload"":true}},""widgets_values"":[""ComfyUI_temp_ervqp_00018_.png"",""image""]},{""id"":44,""type"":""easy icLightApply"",""pos"":[3019.023681640625,825.09130859375],""size"":[210,146],""flags"":{},""order"":10,""mode"":0,""inputs"":[{""label"":""模型"",""localized_name"":""模型"",""name"":""model"",""type"":""MODEL"",""link"":128},{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":95},{""localized_name"":""vae"",""name"":""vae"",""type"":""VAE"",""link"":102},{""localized_name"":""模式"",""name"":""mode"",""type"":""COMBO"",""widget"":{""name"":""mode""},""link"":null},{""localized_name"":""lighting"",""name"":""lighting"",""type"":""COMBO"",""widget"":{""name"":""lighting""},""link"":null},{""localized_name"":""source"",""name"":""source"",""type"":""COMBO"",""widget"":{""name"":""source""},""link"":null},{""localized_name"":""remove_bg"",""name"":""remove_bg"",""type"":""BOOLEAN"",""widget"":{""name"":""remove_bg""},""link"":null}],""outputs"":[{""localized_name"":""模型"",""name"":""model"",""type"":""MODEL"",""slot_index"":0,""links"":[100]},{""localized_name"":""lighting_image"",""name"":""lighting_image"",""type"":""IMAGE"",""links"":null}],""properties"":{""cnr_id"":""comfyui-easy-use"",""ver"":""1c4cb43f7b219048251d52251237a42f22790a62"",""Node name for S&R"":""easy icLightApply"",""widget_ue_connectable"":{""mode"":true,""lighting"":true,""source"":true,""remove_bg"":true}},""widgets_values"":[""Foreground"",""None"",""Use Background Image"",false]},{""id"":73,""type"":""LayerMask: RmBgUltra V2"",""pos"":[3321.5712890625,463.0148010253906],""size"":[270,246],""flags"":{},""order"":6,""mode"":0,""inputs"":[{""localized_name"":""图像"",""name"":""image"",""type"":""IMAGE"",""link"":143},{""localized_name"":""细节方法"",""name"":""detail_method"",""type"":""COMBO"",""widget"":{""name"":""detail_method""},""link"":null},{""localized_name"":""细节腐蚀"",""name"":""detail_erode"",""type"":""INT"",""widget"":{""name"":""detail_erode""},""link"":null},{""localized_name"":""细节膨胀"",""name"":""detail_dilate"",""type"":""INT"",""widget"":{""name"":""detail_dilate""},""link"":null},{""localized_name"":""黑点"",""name"":""black_point"",""type"":""FLOAT"",""widget"":{""name"":""black_point""},""link"":null},{""localized_name"":""白点"",""name"":""white_point"",""type"":""FLOAT"",""widget"":{""name"":""white_point""},""link"":null},{""localized_name"":""处理细节"",""name"":""process_detail"",""type"":""BOOLEAN"",""widget"":{""name"":""process_detail""},""link"":null},{""localized_name"":""设备"",""name"":""device"",""type"":""COMBO"",""widget"":{""name"":""device""},""link"":null},{""localized_name"":""最大像素"",""name"":""max_megapixels"",""type"":""FLOAT"",""widget"":{""name"":""max_megapixels""},""link"":null}],""outputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""links"":null},{""localized_name"":""mask"",""name"":""mask"",""type"":""MASK"",""links"":[144]}],""properties"":{""cnr_id"":""comfyui_layerstyle"",""ver"":""1.0.90"",""Node name for S&R"":""LayerMask: RmBgUltra V2"",""widget_ue_connectable"":{""detail_method"":true,""detail_erode"":true,""detail_dilate"":true,""black_point"":true,""white_point"":true,""process_detail"":true,""device"":true,""max_megapixels"":true}},""widgets_values"":[""VITMatte"",6,6,0.01,0.99,true,""cuda"",2],""color"":""rgba(27, 80, 119, 0.7)""},{""id"":47,""type"":""VAEDecode"",""pos"":[3726.720458984375,839.4774169921875],""size"":[140,46],""flags"":{},""order"":14,""mode"":0,""inputs"":[{""label"":""Latent"",""localized_name"":""Latent"",""name"":""samples"",""type"":""LATENT"",""link"":101},{""label"":""VAE"",""localized_name"":""vae"",""name"":""vae"",""type"":""VAE"",""link"":116}],""outputs"":[{""label"":""图像"",""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[131]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.30"",""Node name for S&R"":""VAEDecode"",""widget_ue_connectable"":{}},""widgets_values"":[]},{""id"":43,""type"":""LG_Relight"",""pos"":[2755.667724609375,809.6322021484375],""size"":[210,126],""flags"":{},""order"":9,""mode"":0,""inputs"":[{""label"":""image"",""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":118},{""label"":""normals"",""localized_name"":""normals"",""name"":""normals"",""type"":""IMAGE"",""link"":93}],""outputs"":[{""label"":""IMAGE"",""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[95,113,148]}],""properties"":{""cnr_id"":""Comfyui-LG_Relight"",""ver"":""de7b21ea5299ae9def09580230ab0eee6ecd07e5"",""Node name for S&R"":""LG_Relight"",""widget_ue_connectable"":{}},""widgets_values"":[8349887901936731,""randomize"",null]},{""id"":75,""type"":""PreviewImage"",""pos"":[3118.224365234375,262.4378662109375],""size"":[140,26],""flags"":{},""order"":12,""mode"":0,""inputs"":[{""localized_name"":""图像"",""name"":""images"",""type"":""IMAGE"",""link"":148}],""outputs"":[],""properties"":{""widget_ue_connectable"":{},""cnr_id"":""comfy-core"",""ver"":""0.3.39"",""Node name for S&R"":""PreviewImage""}},{""id"":62,""type"":""easy imageDetailTransfer"",""pos"":[3616.930908203125,516.719970703125],""size"":[315,194],""flags"":{},""order"":15,""mode"":0,""inputs"":[{""label"":""目标图像"",""localized_name"":""目标图像"",""name"":""target"",""type"":""IMAGE"",""link"":131},{""label"":""源图像"",""localized_name"":""源图像"",""name"":""source"",""type"":""IMAGE"",""link"":132},{""label"":""遮罩"",""localized_name"":""遮罩"",""name"":""mask"",""shape"":7,""type"":""MASK"",""link"":144},{""localized_name"":""模式"",""name"":""mode"",""type"":""COMBO"",""widget"":{""name"":""mode""},""link"":null},{""localized_name"":""模糊"",""name"":""blur_sigma"",""type"":""FLOAT"",""widget"":{""name"":""blur_sigma""},""link"":null},{""localized_name"":""混合"",""name"":""blend_factor"",""type"":""FLOAT"",""widget"":{""name"":""blend_factor""},""link"":null},{""localized_name"":""图像输出"",""name"":""image_output"",""type"":""COMBO"",""widget"":{""name"":""image_output""},""link"":null},{""localized_name"":""save_prefix"",""name"":""save_prefix"",""type"":""STRING"",""widget"":{""name"":""save_prefix""},""link"":null}],""outputs"":[{""label"":""图像"",""localized_name"":""图像"",""name"":""image"",""type"":""IMAGE"",""slot_index"":0,""links"":[149]}],""properties"":{""cnr_id"":""comfyui-easy-use"",""ver"":""1c4cb43f7b219048251d52251237a42f22790a62"",""Node name for S&R"":""easy imageDetailTransfer"",""widget_ue_connectable"":{""mode"":true,""blur_sigma"":true,""blend_factor"":true,""image_output"":true,""save_prefix"":true}},""widgets_values"":[""add"",1,1,""Hide"",""ComfyUI""]},{""id"":76,""type"":""SaveImage"",""pos"":[4047.78369140625,528.5198974609375],""size"":[270,58],""flags"":{},""order"":16,""mode"":0,""inputs"":[{""localized_name"":""图片"",""name"":""images"",""type"":""IMAGE"",""link"":149},{""localized_name"":""文件名前缀"",""name"":""filename_prefix"",""type"":""STRING"",""widget"":{""name"":""filename_prefix""},""link"":null}],""outputs"":[],""title"":""output-image-relight"",""properties"":{""widget_ue_connectable"":{},""cnr_id"":""comfy-core"",""ver"":""0.3.39"",""Node name for S&R"":""SaveImage""},""widgets_values"":[""ComfyUI""]}],""links"":[[89,42,0,14,0,""IMAGE""],[93,14,0,43,1,""IMAGE""],[95,43,0,44,1,""IMAGE""],[100,44,0,49,0,""MODEL""],[101,49,0,47,0,""LATENT""],[102,48,2,44,2,""VAE""],[104,48,1,51,0,""CLIP""],[105,48,1,52,0,""CLIP""],[106,51,0,49,1,""CONDITIONING""],[107,52,0,49,2,""CONDITIONING""],[113,43,0,56,0,""IMAGE""],[114,56,0,49,3,""LATENT""],[115,48,2,56,1,""VAE""],[116,48,2,47,1,""VAE""],[118,42,0,43,0,""IMAGE""],[127,48,0,61,0,""MODEL""],[128,61,0,44,0,""MODEL""],[131,47,0,62,0,""IMAGE""],[132,42,0,62,1,""IMAGE""],[136,42,3,64,0,""*""],[137,42,4,64,1,""*""],[138,64,0,14,3,""INT""],[141,67,0,42,0,""IMAGE""],[143,42,0,73,0,""IMAGE""],[144,73,1,62,2,""MASK""],[148,43,0,75,0,""IMAGE""],[149,62,0,76,0,""IMAGE""]],""groups"":[],""config"":{},""extra"":{""ds"":{""scale"":0.9646149645000017,""offset"":[-2038.000453600284,-54.75567559442037]},""frontendVersion"":""1.18.9"",""workspace_info"":{""id"":""IWpC6ZCDJPX-dqU6dlHuP""},""ue_links"":[],""VHS_latentpreview"":false,""VHS_latentpreviewrate"":0,""VHS_MetadataImage"":true,""VHS_KeepIntermediate"":true},""version"":0.4}";

        /// <summary>
        /// 运行工作流
        /// </summary>
        /// <param name="image_image">input-image-打光原图 - image</param>
        /// <param name="image_upload">input-image-打光原图 - upload</param>
        /// <returns>任务ID</returns>
        public static string runWorkflow(string image_image = "ComfyUI_temp_ervqp_00018_.png", string image_upload = "image")
        {
            try
            {
                // 解析工作流JSON
                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);
                if (workflow == null)
                {
                    throw new Exception("无法解析工作流JSON");
                }

                // 更新输入参数
                // 检查JSON格式并相应更新参数
                if (workflow["nodes"] != null)
                {
                    // 新格式：包含nodes数组
                    var nodesArray = workflow["nodes"] as JArray;
                    if (nodesArray != null)
                    {
                        // 更新节点 67 的参数
                        var node67 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "67") as JObject;
                        if (node67 != null)
                        {
                            var inputs67 = node67["inputs"] as JArray;
                            var widgetValues67 = node67["widgets_values"] as JArray;
                            if (inputs67 != null && widgetValues67 != null)
                            {
                                // 更新 image 参数
                                var inputIndeximage = inputs67.ToList().FindIndex(i => i["name"]?.ToString() == "image");
                                if (inputIndeximage >= 0 && inputIndeximage < widgetValues67.Count)
                                {
                                    widgetValues67[inputIndeximage] = JToken.FromObject(image_image);
                                }

                                // 更新 upload 参数
                                var inputIndexupload = inputs67.ToList().FindIndex(i => i["name"]?.ToString() == "upload");
                                if (inputIndexupload >= 0 && inputIndexupload < widgetValues67.Count)
                                {
                                    widgetValues67[inputIndexupload] = JToken.FromObject(image_upload);
                                }

                            }
                        }

                    }
                }
                else
                {
                    // 旧格式：直接以节点ID为键
                    // 更新节点 67 的 image 参数
                    if (workflow["67"]?["inputs"]?["image"] != null)
                    {
                        workflow["67"]["inputs"]["image"] = JToken.FromObject(image_image);
                    }

                    // 更新节点 67 的 upload 参数
                    if (workflow["67"]?["inputs"]?["upload"] != null)
                    {
                        workflow["67"]["inputs"]["upload"] = JToken.FromObject(image_upload);
                    }

                }
                // 提交工作流到ComfyUI
                string updatedJson = JsonConvert.SerializeObject(workflow);
                var comfyUIManage = ComfyUIManage.Instance;

                // 首先将工作流保存到数据库（如果不存在）
                string workflowId = EnsureWorkflowExists(updatedJson, "Relight");
                if (string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine("无法保存工作流到数据库");
                    return "";
                }

                // 创建任务
                string taskId = comfyUIManage.CreateTask(workflowId, "Relight_Task", "system");
                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("无法创建任务");
                    return "";
                }

                Console.WriteLine($"工作流已提交，任务ID: {taskId}");
                return taskId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行工作流失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态信息</returns>
        public static string GetTaskStatus(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    return JsonConvert.SerializeObject(task, Formatting.Indented);
                }
                return "任务不存在";
            }
            catch (Exception ex)
            {
                return $"获取任务状态失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 确保工作流存在于数据库中
        /// </summary>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流ID</returns>
        private static string EnsureWorkflowExists(string workflowJson, string workflowName)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 计算工作流的哈希值作为唯一标识
                string workflowHash = System.Security.Cryptography.SHA1.Create()
                    .ComputeHash(System.Text.Encoding.UTF8.GetBytes(workflowJson))
                    .Aggregate("", (s, b) => s + b.ToString("x2"));

                // 检查是否已存在
                var existingWorkflow = comfyUIManage.GetWorkflowById(workflowHash);
                if (existingWorkflow != null)
                {
                    Console.WriteLine($"工作流已存在: {workflowHash}");
                    return workflowHash;
                }

                // 创建新的工作流
                string workflowId = comfyUIManage.AddWorkflow(
                    workflowName,
                    workflowJson,
                    "generated",
                    $"自动生成的工作流: {workflowName}",
                    "system"
                );

                Console.WriteLine($"创建新工作流: {workflowId}");
                return workflowId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"确保工作流存在失败: {ex.Message}");
                return "";
            }
        }

    }
}
