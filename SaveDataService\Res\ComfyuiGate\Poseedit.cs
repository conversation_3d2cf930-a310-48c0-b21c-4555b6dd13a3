﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService;
using SaveDataService.Manage;
using SaveDataService.Server.Websocket;

namespace ComfyuiGate
{
    /// <summary>
    /// Poseedit - ComfyUI工作流调用类
    /// 基于文件: poseedit.json
    /// 自动生成时间: 2025-06-05 17:32:58
    /// 继承RESTfulAPIBase，自动提供RESTful API功能
    /// </summary>
    public class Poseedit : RESTfulAPIBase
    {
        /// <summary>
        /// 工作流JSON定义
        /// </summary>
        private const string WORKFLOW_JSON = @"{""id"":""4016bf47-4c36-400d-bb48-0237fd656be3"",""revision"":0,""last_node_id"":26,""last_link_id"":33,""nodes"":[{""id"":8,""type"":""VAEDecode"",""pos"":[1057,42],""size"":[210,46],""flags"":{},""order"":13,""mode"":0,""inputs"":[{""localized_name"":""Latent"",""name"":""samples"",""type"":""LATENT"",""link"":7},{""localized_name"":""vae"",""name"":""vae"",""type"":""VAE"",""link"":8}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[10]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""VAEDecode"",""widget_ue_connectable"":{}},""widgets_values"":[]},{""id"":14,""type"":""PreviewImage"",""pos"":[1443,123],""size"":[550,937],""flags"":{},""order"":14,""mode"":0,""inputs"":[{""localized_name"":""图像"",""name"":""images"",""type"":""IMAGE"",""link"":10}],""outputs"":[],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""PreviewImage"",""widget_ue_connectable"":{}},""widgets_values"":[]},{""id"":5,""type"":""EmptyLatentImage"",""pos"":[689,15],""size"":[315,106],""flags"":{},""order"":10,""mode"":0,""inputs"":[{""localized_name"":""宽度"",""name"":""width"",""type"":""INT"",""widget"":{""name"":""width""},""link"":26},{""localized_name"":""高度"",""name"":""height"",""type"":""INT"",""widget"":{""name"":""height""},""link"":27},{""localized_name"":""批量大小"",""name"":""batch_size"",""type"":""INT"",""widget"":{""name"":""batch_size""},""link"":null}],""outputs"":[{""localized_name"":""Latent"",""name"":""LATENT"",""type"":""LATENT"",""slot_index"":0,""links"":[2]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""EmptyLatentImage"",""widget_ue_connectable"":{""width"":true,""height"":true,""batch_size"":true}},""widgets_values"":[512,512,1]},{""id"":4,""type"":""CheckpointLoaderSimple"",""pos"":[244,-2],""size"":[315,98],""flags"":{},""order"":0,""mode"":0,""inputs"":[{""localized_name"":""Checkpoint名称"",""name"":""ckpt_name"",""type"":""COMBO"",""widget"":{""name"":""ckpt_name""},""link"":null}],""outputs"":[{""localized_name"":""模型"",""name"":""MODEL"",""type"":""MODEL"",""slot_index"":0,""links"":[1]},{""localized_name"":""CLIP"",""name"":""CLIP"",""type"":""CLIP"",""slot_index"":1,""links"":[20,22]},{""localized_name"":""VAE"",""name"":""VAE"",""type"":""VAE"",""slot_index"":2,""links"":[8]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""CheckpointLoaderSimple"",""widget_ue_connectable"":{""ckpt_name"":true}},""widgets_values"":[""IL\\illustrij14.Oqel.safetensors""]},{""id"":3,""type"":""KSampler"",""pos"":[1117.133056640625,360.06683349609375],""size"":[315,262],""flags"":{},""order"":12,""mode"":0,""inputs"":[{""localized_name"":""模型"",""name"":""model"",""type"":""MODEL"",""link"":1},{""localized_name"":""正面条件"",""name"":""positive"",""type"":""CONDITIONING"",""link"":13},{""localized_name"":""负面条件"",""name"":""negative"",""type"":""CONDITIONING"",""link"":23},{""localized_name"":""Latent图像"",""name"":""latent_image"",""type"":""LATENT"",""link"":2},{""localized_name"":""种子"",""name"":""seed"",""type"":""INT"",""widget"":{""name"":""seed""},""link"":null},{""localized_name"":""步数"",""name"":""steps"",""type"":""INT"",""widget"":{""name"":""steps""},""link"":null},{""localized_name"":""cfg"",""name"":""cfg"",""type"":""FLOAT"",""widget"":{""name"":""cfg""},""link"":null},{""localized_name"":""采样器名称"",""name"":""sampler_name"",""type"":""COMBO"",""widget"":{""name"":""sampler_name""},""link"":null},{""localized_name"":""调度器"",""name"":""scheduler"",""type"":""COMBO"",""widget"":{""name"":""scheduler""},""link"":null},{""localized_name"":""降噪"",""name"":""denoise"",""type"":""FLOAT"",""widget"":{""name"":""denoise""},""link"":null}],""outputs"":[{""localized_name"":""Latent"",""name"":""LATENT"",""type"":""LATENT"",""slot_index"":0,""links"":[7]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""KSampler"",""widget_ue_connectable"":{""seed"":true,""steps"":true,""cfg"":true,""sampler_name"":true,""scheduler"":true,""denoise"":true}},""widgets_values"":[46806059861263,""randomize"",20,8,""euler"",""normal"",1]},{""id"":18,""type"":""CLIPTextEncode"",""pos"":[677.75146484375,184.93992614746094],""size"":[400,200],""flags"":{},""order"":9,""mode"":0,""inputs"":[{""localized_name"":""clip"",""name"":""clip"",""type"":""CLIP"",""link"":20},{""localized_name"":""文本"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":31}],""outputs"":[{""localized_name"":""条件"",""name"":""CONDITIONING"",""type"":""CONDITIONING"",""links"":[21]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""CLIPTextEncode"",""widget_ue_connectable"":{""text"":true}},""widgets_values"":[""1girl,anime"",[false,true]]},{""id"":21,""type"":""GetImageSize+"",""pos"":[407.2521667480469,162.89208984375],""size"":[159.50155639648438,66],""flags"":{},""order"":6,""mode"":0,""inputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""IMAGE"",""link"":25}],""outputs"":[{""localized_name"":""width"",""name"":""width"",""type"":""INT"",""links"":[26]},{""localized_name"":""height"",""name"":""height"",""type"":""INT"",""links"":[27]},{""localized_name"":""count"",""name"":""count"",""type"":""INT"",""links"":null}],""properties"":{""cnr_id"":""comfyui_essentials"",""ver"":""1.1.0"",""Node name for S&R"":""GetImageSize+"",""widget_ue_connectable"":{}},""widgets_values"":[]},{""id"":15,""type"":""PoseNode"",""pos"":[-31.490646362304688,327.5979309082031],""size"":[280.32025146484375,420.32025146484375],""flags"":{},""order"":1,""mode"":0,""inputs"":[{""localized_name"":""image"",""name"":""image"",""type"":""COMBO"",""widget"":{""name"":""image""},""link"":null}],""outputs"":[{""localized_name"":""图像"",""name"":""IMAGE"",""type"":""IMAGE"",""slot_index"":0,""links"":[15,25]},{""localized_name"":""遮罩"",""name"":""MASK"",""type"":""MASK"",""links"":null}],""title"":""input-PoseNode-特殊组件"",""properties"":{""cnr_id"":""comfyui_custom_nodes_alekpet"",""ver"":""1.0.66"",""Node name for S&R"":""PoseNode"",""widget_ue_connectable"":{""image"":true}},""widgets_values"":[""Pose_15.png"",""add_pose"",""reset_pose"",{""undo_history"":[{""keypoints"":[[241,77],[241,120],[191,118],[177,183],[163,252],[298,118],[317,182],[332,245],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[241,120],[191,118],[177,183],[163,252],[298,118],[317,182],[332,245],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[241,120],[191,118],[177,183],[163,252],[298,118],[317,182],[354,311],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[191,118],[177,183],[163,252],[298,118],[317,182],[354,311],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[176,224],[177,183],[163,252],[298,118],[317,182],[354,311],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[176,224],[140,244],[163,252],[298,118],[317,182],[354,311],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[176,224],[140,244],[190,465],[298,118],[317,182],[354,311],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[176,224],[131,353],[190,465],[298,118],[317,182],[354,311],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[147,254],[131,353],[190,465],[298,118],[317,182],[354,311],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[147,254],[131,353],[190,465],[321,260],[317,182],[354,311],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[354,311],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[225,241],[66,423],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[225,241],[66,423],[142,607],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[225,241],[167,508],[142,607],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[241,77],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[224,402],[167,508],[142,607],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[224,402],[167,508],[142,607],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[224,402],[167,508],[142,607],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[224,402],[167,508],[142,607],[270,240],[282,360],[286,456],[232,59],[263,154],[225,70],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[224,402],[167,508],[142,607],[270,240],[282,360],[286,456],[232,59],[263,154],[86,164],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[224,402],[167,508],[142,607],[270,240],[282,360],[286,456],[142,153],[263,154],[86,164],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[224,402],[167,508],[142,607],[269,500],[282,360],[286,456],[142,153],[263,154],[86,164],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[207,505],[167,508],[142,607],[269,500],[282,360],[286,456],[142,153],[263,154],[86,164],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[207,505],[196,600],[142,607],[269,500],[282,360],[286,456],[142,153],[263,154],[86,164],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[207,505],[196,600],[142,607],[269,500],[320,577],[286,456],[142,153],[263,154],[86,164],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[207,505],[196,600],[142,607],[269,500],[320,577],[368,616],[142,153],[263,154],[86,164],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[207,505],[196,600],[142,607],[425,426],[320,577],[368,616],[142,153],[263,154],[86,164],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[240,512],[196,600],[142,607],[425,426],[320,577],[368,616],[142,153],[263,154],[86,164],[330,160]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[240,512],[196,600],[142,607],[426,487],[320,577],[368,616],[142,153],[263,154],[86,164],[330,160]]},{""keypoints"":[[241,77],[241,120],[191,118],[177,183],[163,252],[298,118],[317,182],[332,245],[225,241],[213,359],[215,454],[270,240],[282,360],[286,456],[232,59],[253,60],[225,70],[260,72]]},{""keypoints"":[[195,200],[209,253],[147,254],[131,353],[190,465],[321,260],[326,370],[245,489],[240,512],[196,600],[142,607],[426,487],[320,577],[368,616],[142,153],[263,154],[86,164],[330,160]]}],""redo_history"":[],""currentCanvasSize"":{""width"":512,""height"":512},""background"":""/api/view?filename=ComfyUI_temp_eimqy_00006_.png&type=input&subfolder=&rand=0.1586942866868224""}]},{""id"":24,""type"":""CR Text"",""pos"":[251.05026245117188,435.5204162597656],""size"":[400,200],""flags"":{},""order"":2,""mode"":0,""inputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":null}],""outputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""*"",""links"":[32]},{""localized_name"":""show_help"",""name"":""show_help"",""type"":""STRING"",""links"":null}],""title"":""input-text-负面提示词"",""properties"":{""widget_ue_connectable"":{},""cnr_id"":""ComfyUI_Comfyroll_CustomNodes"",""ver"":""d78b780ae43fcf8c6b7c6505e6ffb4584281ceca"",""Node name for S&R"":""CR Text""},""widgets_values"":["""",[false,true]]},{""id"":13,""type"":""ControlNetLoader"",""pos"":[-159.9451904296875,827.0906372070312],""size"":[315,58],""flags"":{},""order"":3,""mode"":0,""inputs"":[{""localized_name"":""ControlNet名称"",""name"":""control_net_name"",""type"":""COMBO"",""widget"":{""name"":""control_net_name""},""link"":null}],""outputs"":[{""localized_name"":""ControlNet"",""name"":""CONTROL_NET"",""type"":""CONTROL_NET"",""slot_index"":0,""links"":[28]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""ControlNetLoader"",""widget_ue_connectable"":{""control_net_name"":true}},""widgets_values"":[""XL\\diffusion_pytorch_model_promax.safetensors""]},{""id"":22,""type"":""SetUnionControlNetType"",""pos"":[188.9552001953125,821.3115234375],""size"":[270,58],""flags"":{},""order"":8,""mode"":0,""inputs"":[{""localized_name"":""ControlNet"",""name"":""control_net"",""type"":""CONTROL_NET"",""link"":28},{""localized_name"":""类型"",""name"":""type"",""type"":""COMBO"",""widget"":{""name"":""type""},""link"":null}],""outputs"":[{""localized_name"":""ControlNet"",""name"":""CONTROL_NET"",""type"":""CONTROL_NET"",""links"":[29]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""SetUnionControlNetType"",""widget_ue_connectable"":{""type"":true}},""widgets_values"":[""openpose""]},{""id"":19,""type"":""CLIPTextEncode"",""pos"":[607.0342407226562,671.7351684570312],""size"":[400,200],""flags"":{},""order"":7,""mode"":0,""inputs"":[{""localized_name"":""clip"",""name"":""clip"",""type"":""CLIP"",""link"":22},{""localized_name"":""文本"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":32}],""outputs"":[{""localized_name"":""条件"",""name"":""CONDITIONING"",""type"":""CONDITIONING"",""links"":[23]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""CLIPTextEncode"",""widget_ue_connectable"":{""text"":true}},""widgets_values"":["""",[false,true]]},{""id"":23,""type"":""CR Text"",""pos"":[-142.28306579589844,-7.812896728515625],""size"":[400,200],""flags"":{},""order"":4,""mode"":0,""inputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":null}],""outputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""*"",""links"":[31]},{""localized_name"":""show_help"",""name"":""show_help"",""type"":""STRING"",""links"":null}],""title"":""input-text-正向提示词"",""properties"":{""widget_ue_connectable"":{},""cnr_id"":""ComfyUI_Comfyroll_CustomNodes"",""ver"":""d78b780ae43fcf8c6b7c6505e6ffb4584281ceca"",""Node name for S&R"":""CR Text""},""widgets_values"":["""",[false,true]]},{""id"":11,""type"":""ControlNetApply"",""pos"":[736.5913696289062,485.274658203125],""size"":[317.4000244140625,98],""flags"":{},""order"":11,""mode"":0,""inputs"":[{""localized_name"":""条件"",""name"":""conditioning"",""type"":""CONDITIONING"",""link"":21},{""localized_name"":""ControlNet"",""name"":""control_net"",""type"":""CONTROL_NET"",""link"":29},{""localized_name"":""图像"",""name"":""image"",""type"":""IMAGE"",""link"":15},{""localized_name"":""强度"",""name"":""strength"",""type"":""FLOAT"",""widget"":{""name"":""strength""},""link"":33}],""outputs"":[{""localized_name"":""条件"",""name"":""CONDITIONING"",""type"":""CONDITIONING"",""slot_index"":0,""links"":[13]}],""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.33"",""Node name for S&R"":""ControlNetApply"",""widget_ue_connectable"":{""strength"":true}},""widgets_values"":[1]},{""id"":26,""type"":""easy float"",""pos"":[257.71710205078125,714.1871337890625],""size"":[270,58],""flags"":{},""order"":5,""mode"":0,""inputs"":[{""localized_name"":""值"",""name"":""value"",""type"":""FLOAT"",""widget"":{""name"":""value""},""link"":null}],""outputs"":[{""localized_name"":""浮点"",""name"":""float"",""type"":""FLOAT"",""links"":[33]}],""title"":""input-float-controlnet强度"",""properties"":{""widget_ue_connectable"":{},""cnr_id"":""comfyui-easy-use"",""ver"":""1.3.0"",""Node name for S&R"":""easy float""},""widgets_values"":[1.0000000000000002]}],""links"":[[1,4,0,3,0,""MODEL""],[2,5,0,3,3,""LATENT""],[7,3,0,8,0,""LATENT""],[8,4,2,8,1,""VAE""],[10,8,0,14,0,""IMAGE""],[13,11,0,3,1,""CONDITIONING""],[15,15,0,11,2,""IMAGE""],[20,4,1,18,0,""CLIP""],[21,18,0,11,0,""CONDITIONING""],[22,4,1,19,0,""CLIP""],[23,19,0,3,2,""CONDITIONING""],[25,15,0,21,0,""IMAGE""],[26,21,0,5,0,""INT""],[27,21,1,5,1,""INT""],[28,13,0,22,0,""CONTROL_NET""],[29,22,0,11,1,""CONTROL_NET""],[31,23,0,18,1,""STRING""],[32,24,0,19,1,""STRING""],[33,26,0,11,3,""FLOAT""]],""groups"":[],""config"":{},""extra"":{""frontendVersion"":""1.18.9"",""VHS_latentpreview"":false,""VHS_latentpreviewrate"":0,""VHS_MetadataImage"":true,""VHS_KeepIntermediate"":true,""ue_links"":[],""ds"":{""scale"":1,""offset"":[735.6162533319284,72.81289368870722]}},""version"":0.4}";

        /// <summary>
        /// 运行工作流
        /// </summary>
        /// <param name="PoseNode_image">input-PoseNode-特殊组件 - image</param>
        /// <param name="text_text">input-text-负面提示词 - text</param>
        /// <param name="text_text1">input-text-正向提示词 - text</param>
        /// <param name="float_value">input-float-controlnet强度 - value</param>
        /// <returns>任务ID</returns>
        public static string runWorkflow(string PoseNode_image = "Pose_15.png", string text_text = "", string text_text1 = "", double float_value = 1.0)
        {
            try
            {
                // 解析工作流JSON
                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);
                if (workflow == null)
                {
                    throw new Exception("无法解析工作流JSON");
                }

                // 更新输入参数
                // 检查JSON格式并相应更新参数
                if (workflow["nodes"] != null)
                {
                    // 新格式：包含nodes数组
                    var nodesArray = workflow["nodes"] as JArray;
                    if (nodesArray != null)
                    {
                        // 更新节点 15 的参数
                        var node15 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "15") as JObject;
                        if (node15 != null)
                        {
                            var inputs15 = node15["inputs"] as JArray;
                            var widgetValues15 = node15["widgets_values"] as JArray;
                            if (inputs15 != null && widgetValues15 != null)
                            {
                                // 更新 image 参数
                                var inputIndeximage = inputs15.ToList().FindIndex(i => i["name"]?.ToString() == "image");
                                if (inputIndeximage >= 0 && inputIndeximage < widgetValues15.Count)
                                {
                                    widgetValues15[inputIndeximage] = JToken.FromObject(PoseNode_image);
                                }

                            }
                        }

                        // 更新节点 24 的参数
                        var node24 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "24") as JObject;
                        if (node24 != null)
                        {
                            var inputs24 = node24["inputs"] as JArray;
                            var widgetValues24 = node24["widgets_values"] as JArray;
                            if (inputs24 != null && widgetValues24 != null)
                            {
                                // 更新 text 参数
                                var inputIndextext = inputs24.ToList().FindIndex(i => i["name"]?.ToString() == "text");
                                if (inputIndextext >= 0 && inputIndextext < widgetValues24.Count)
                                {
                                    widgetValues24[inputIndextext] = JToken.FromObject(text_text);
                                }

                            }
                        }

                        // 更新节点 23 的参数
                        var node23 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "23") as JObject;
                        if (node23 != null)
                        {
                            var inputs23 = node23["inputs"] as JArray;
                            var widgetValues23 = node23["widgets_values"] as JArray;
                            if (inputs23 != null && widgetValues23 != null)
                            {
                                // 更新 text 参数
                                var inputIndextext = inputs23.ToList().FindIndex(i => i["name"]?.ToString() == "text");
                                if (inputIndextext >= 0 && inputIndextext < widgetValues23.Count)
                                {
                                    widgetValues23[inputIndextext] = JToken.FromObject(text_text1);
                                }

                            }
                        }

                        // 更新节点 26 的参数
                        var node26 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "26") as JObject;
                        if (node26 != null)
                        {
                            var inputs26 = node26["inputs"] as JArray;
                            var widgetValues26 = node26["widgets_values"] as JArray;
                            if (inputs26 != null && widgetValues26 != null)
                            {
                                // 更新 value 参数
                                var inputIndexvalue = inputs26.ToList().FindIndex(i => i["name"]?.ToString() == "value");
                                if (inputIndexvalue >= 0 && inputIndexvalue < widgetValues26.Count)
                                {
                                    widgetValues26[inputIndexvalue] = JToken.FromObject(float_value);
                                }

                            }
                        }

                    }
                }
                else
                {
                    // 旧格式：直接以节点ID为键
                    // 更新节点 15 的 image 参数
                    if (workflow["15"]?["inputs"]?["image"] != null)
                    {
                        workflow["15"]["inputs"]["image"] = JToken.FromObject(PoseNode_image);
                    }

                    // 更新节点 24 的 text 参数
                    if (workflow["24"]?["inputs"]?["text"] != null)
                    {
                        workflow["24"]["inputs"]["text"] = JToken.FromObject(text_text);
                    }

                    // 更新节点 23 的 text 参数
                    if (workflow["23"]?["inputs"]?["text"] != null)
                    {
                        workflow["23"]["inputs"]["text"] = JToken.FromObject(text_text1);
                    }

                    // 更新节点 26 的 value 参数
                    if (workflow["26"]?["inputs"]?["value"] != null)
                    {
                        workflow["26"]["inputs"]["value"] = JToken.FromObject(float_value);
                    }

                }
                // 提交工作流到ComfyUI
                string updatedJson = JsonConvert.SerializeObject(workflow);
                var comfyUIManage = ComfyUIManage.Instance;

                // 首先将工作流保存到数据库（如果不存在）
                string workflowId = EnsureWorkflowExists(updatedJson, "Poseedit");
                if (string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine("无法保存工作流到数据库");
                    return "";
                }

                // 创建任务
                string taskId = comfyUIManage.CreateTask(workflowId, "Poseedit_Task", "system");
                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("无法创建任务");
                    return "";
                }

                Console.WriteLine($"工作流已提交，任务ID: {taskId}");
                return taskId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行工作流失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态信息</returns>
        public static string GetTaskStatus(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    return JsonConvert.SerializeObject(task, Formatting.Indented);
                }
                return "任务不存在";
            }
            catch (Exception ex)
            {
                return $"获取任务状态失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 确保工作流存在于数据库中
        /// </summary>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流ID</returns>
        private static string EnsureWorkflowExists(string workflowJson, string workflowName)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 计算工作流的哈希值作为唯一标识
                string workflowHash = System.Security.Cryptography.SHA1.Create()
                    .ComputeHash(System.Text.Encoding.UTF8.GetBytes(workflowJson))
                    .Aggregate("", (s, b) => s + b.ToString("x2"));

                // 检查是否已存在
                var existingWorkflow = comfyUIManage.GetWorkflowById(workflowHash);
                if (existingWorkflow != null)
                {
                    Console.WriteLine($"工作流已存在: {workflowHash}");
                    return workflowHash;
                }

                // 创建新的工作流
                string workflowId = comfyUIManage.AddWorkflow(
                    workflowName,
                    workflowJson,
                    "generated",
                    $"自动生成的工作流: {workflowName}",
                    "system"
                );

                Console.WriteLine($"创建新工作流: {workflowId}");
                return workflowId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"确保工作流存在失败: {ex.Message}");
                return "";
            }
        }

    }
}
